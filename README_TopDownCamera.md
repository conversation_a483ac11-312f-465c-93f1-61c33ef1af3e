# Top-Down Camera System for Roblox

This project includes a professional top-down camera system that automatically follows the player's character from above.

## 📁 File Structure

```
src/
├── client/
│   ├── init.client.luau          # Main client initialization
│   ├── TopDownCamera.luau        # Top-down camera module
│   └── CameraController.luau     # Optional keyboard controls
├── server/
└── shared/
```

## 🚀 How to Use in Roblox Studio

### Method 1: Using Rojo (Recommended)
1. Make sure you have Rojo installed and configured
2. Run `rojo serve` in your project directory
3. Connect to Rojo in Roblox Studio
4. The camera system will automatically load when you test the game

### Method 2: Manual Setup
1. In Roblox Studio, go to `StarterPlayer > StarterPlayerScripts`
2. Create a new LocalScript called "CameraSystem"
3. Copy the contents of `src/client/init.client.luau` into this script
4. Create ModuleScripts for `TopDownCamera` and `CameraController` (optional)
5. Copy the respective code into each module

## 🎮 Features

- **Automatic Top-Down View**: Camera automatically positions above the player
- **Smooth Movement**: Lerped camera movement for smooth following
- **Character Respawn Handling**: Camera repositions when player respawns
- **Configurable Settings**: Easy to adjust height, angle, and smoothness
- **Enable/Disable Functions**: Can be controlled programmatically
- **Optional Keyboard Controls**: Toggle and adjust camera with keys

## ⌨️ Keyboard Controls (Optional)

To enable keyboard controls, uncomment this line in `init.client.luau`:
```lua
require(script.CameraController)
```

**Controls:**
- `C` - Toggle between top-down and default camera
- `Q` - Increase camera height
- `E` - Decrease camera height

## ⚙️ Configuration

You can adjust the camera behavior by modifying the CONFIG table in `TopDownCamera.luau`:

```lua
local CONFIG = {
    HEIGHT = 25,                    -- Height above character
    ANGLE = math.rad(75),          -- Camera angle (75° for top-down)
    SMOOTHNESS = 0.15,             -- Smoothing factor (0-1)
    OFFSET = Vector3.new(0, 0, 0), -- Position offset
    MIN_HEIGHT = 10,               -- Minimum height
    MAX_HEIGHT = 50,               -- Maximum height
}
```

## 🔧 Programmatic Control

The TopDownCamera module returns functions for external control:

```lua
local TopDownCamera = require(path.to.TopDownCamera)

-- Control functions
TopDownCamera.enable()                    -- Enable camera
TopDownCamera.disable()                   -- Disable camera
TopDownCamera.setHeight(30)              -- Set height
TopDownCamera.setAngle(60)               -- Set angle (degrees)
TopDownCamera.setSmoothness(0.2)         -- Set smoothing
TopDownCamera.setOffset(Vector3.new(0,0,5)) -- Set offset
```

## 🎯 Perfect For

- Strategy games
- RPGs with overhead view
- City builders
- Any game requiring top-down perspective

## 🔄 How It Works

1. The camera positions itself above the player's character
2. Uses `CFrame.lookAt()` to always point down at the character
3. Smoothly follows the character using `CFrame:Lerp()`
4. Handles character respawning automatically
5. Maintains consistent height and angle

The system is designed to be plug-and-play while remaining highly customizable for your specific game needs!
