{"name": "RobloxProject", "className": "DataModel", "filePaths": ["default.project.json"], "children": [{"name": "Lighting", "className": "Lighting"}, {"name": "ReplicatedStorage", "className": "ReplicatedStorage", "children": [{"name": "Shared", "className": "Folder", "children": [{"name": "Hello", "className": "ModuleScript", "filePaths": ["src/shared\\Hello.luau"]}]}]}, {"name": "ServerScriptService", "className": "ServerScriptService", "children": [{"name": "Server", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\init.server.luau"], "children": [{"name": "BlockSpawner", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\BlockSpawner.server.luau"]}, {"name": "CharacterModifier", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\CharacterModifier.server.luau"]}, {"name": "GameManager", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\GameManager.server.luau"]}, {"name": "HealthSystem", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\HealthSystem.server.luau"]}, {"name": "SimpleBlockSpawner", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\SimpleBlockSpawner.server.luau"]}]}]}, {"name": "SoundService", "className": "SoundService"}, {"name": "StarterPlayer", "className": "StarterPlayer", "children": [{"name": "StarterPlayerScripts", "className": "StarterPlayerScripts", "children": [{"name": "Client", "className": "LocalScript", "filePaths": ["src/client\\init.client.luau"], "children": [{"name": "CameraController", "className": "ModuleScript", "filePaths": ["src/client\\CameraController.luau"]}, {"name": "GameUI", "className": "LocalScript", "filePaths": ["src/client\\GameUI.client.luau"]}, {"name": "HealthUI", "className": "LocalScript", "filePaths": ["src/client\\HealthUI.client.luau"]}, {"name": "LoadingScreen", "className": "LocalScript", "filePaths": ["src/client\\LoadingScreen.client.luau"]}, {"name": "TopDownCamera", "className": "ModuleScript", "filePaths": ["src/client\\TopDownCamera.luau"]}]}]}]}, {"name": "Workspace", "className": "Workspace", "children": [{"name": "Baseplate", "className": "Part"}]}]}