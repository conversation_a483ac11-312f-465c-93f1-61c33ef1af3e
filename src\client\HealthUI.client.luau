-- Client-side Health UI System
-- Creates and manages the health bar UI above character

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer

-- Wait for RemoteEvents
local healthEvents = ReplicatedStorage:WaitFor<PERSON>hild("HealthEvents")
local updateHealthEvent = healthEvents:WaitForChild("UpdateHealth")
local playerDeathEvent = healthEvents:WaitForChild("PlayerDeath")

-- UI Configuration
local UI_CONFIG = {
	HEALTH_BAR_SIZE = Vector3.new(6, 0.8, 0), -- 3D size for BillboardGui
	BACKGROUND_COLOR = Color3.fromRGB(50, 50, 50),
	HEALTH_COLOR = Color3.fromRGB(0, 255, 0), -- Green
	LOW_HEALTH_COLOR = Color3.fromRGB(255, 100, 100), -- Red
	LOW_HEALTH_THRESHOLD = 25, -- When to turn red
	ANIMATION_TIME = 0.5, -- Slower animation for smoother fade
	FADE_ANIMATION_TIME = 0.8, -- Fade animation time
	HEIGHT_OFFSET = 8, -- Studs above character
}

local HealthUI = {}
local billboardGui = nil
local healthBar = nil
local healthText = nil
local currentHealth = 100
local maxHealth = 100
local updateConnection = nil

-- Function to create the health UI above character
local function createHealthUI()
	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then
		return
	end

	-- Create BillboardGui (3D UI that follows character)
	billboardGui = Instance.new("BillboardGui")
	billboardGui.Name = "HealthUI"
	billboardGui.Size = UDim2.new(0, 250, 0, 60) -- Made bigger: 250x60 instead of 200x50
	billboardGui.StudsOffset = Vector3.new(0, 4, 0) -- 4 studs above character
	billboardGui.Adornee = character.HumanoidRootPart
	billboardGui.Parent = workspace

	-- Create health bar background
	local healthBarBG = Instance.new("Frame")
	healthBarBG.Name = "HealthBarBackground"
	healthBarBG.Size = UDim2.new(1, 0, 0.6, 0)
	healthBarBG.Position = UDim2.new(0, 0, 0.2, 0)
	healthBarBG.BackgroundColor3 = UI_CONFIG.BACKGROUND_COLOR
	healthBarBG.BorderSizePixel = 0 -- Remove default border
	healthBarBG.Parent = billboardGui

	-- Add rounded corners to background
	local bgCorner = Instance.new("UICorner")
	bgCorner.CornerRadius = UDim.new(0, 8)
	bgCorner.Parent = healthBarBG

	-- Add outline stroke
	local bgStroke = Instance.new("UIStroke")
	bgStroke.Color = Color3.fromRGB(255, 255, 255)
	bgStroke.Thickness = 2
	bgStroke.ApplyStrokeMode = Enum.ApplyStrokeMode.Border
	bgStroke.Parent = healthBarBG

	-- Create health bar fill
	healthBar = Instance.new("Frame")
	healthBar.Name = "HealthBar"
	healthBar.Size = UDim2.new(1, 0, 1, 0)
	healthBar.Position = UDim2.new(0, 0, 0, 0)
	healthBar.BackgroundColor3 = UI_CONFIG.HEALTH_COLOR
	healthBar.BorderSizePixel = 0
	healthBar.Parent = healthBarBG

	-- Add rounded corners to health bar
	local healthCorner = Instance.new("UICorner")
	healthCorner.CornerRadius = UDim.new(0, 6)
	healthCorner.Parent = healthBar

	-- Add subtle inner outline to health bar
	local healthStroke = Instance.new("UIStroke")
	healthStroke.Color = Color3.fromRGB(200, 200, 200)
	healthStroke.Thickness = 1
	healthStroke.ApplyStrokeMode = Enum.ApplyStrokeMode.Border
	healthStroke.Transparency = 0.3
	healthStroke.Parent = healthBar

	-- Create health text
	healthText = Instance.new("TextLabel")
	healthText.Name = "HealthText"
	healthText.Size = UDim2.new(1, 0, 1, 0)
	healthText.Position = UDim2.new(0, 0, 0, 0)
	healthText.BackgroundTransparency = 1
	healthText.Text = "100 / 100" -- Start with full health display
	healthText.TextColor3 = Color3.fromRGB(255, 255, 255)
	healthText.TextScaled = true
	healthText.Font = Enum.Font.GothamBold
	healthText.TextStrokeTransparency = 0
	healthText.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
	healthText.Parent = healthBarBG

	print("Health UI created above character")
end

-- Function to update health display
local function updateHealthDisplay(health, maxHP)
	if not healthBar or not healthText then
		print("Health UI elements not found - recreating UI")
		-- Try to recreate UI if elements are missing
		if player.Character then
			createHealthUI()
		end
		return
	end

	print("Updating health display: " .. health .. "/" .. maxHP .. " (was " .. currentHealth .. ")")

	-- Store previous health for damage detection
	local previousHealth = currentHealth
	currentHealth = health
	maxHealth = maxHP

	-- Calculate health percentage
	local healthPercent = health / maxHP

	-- Create smooth fade animation for health bar size
	local targetSize = UDim2.new(healthPercent, 0, 1, 0)

	-- Use different animation styles based on whether health is decreasing or increasing
	local animationStyle = Enum.EasingStyle.Quad
	local animationDirection = Enum.EasingDirection.Out
	local animationTime = UI_CONFIG.ANIMATION_TIME

	if health < previousHealth then
		-- Health decreasing - use fade out effect
		animationStyle = Enum.EasingStyle.Sine
		animationDirection = Enum.EasingDirection.InOut
		animationTime = UI_CONFIG.FADE_ANIMATION_TIME
	end

	local sizeTween = TweenService:Create(
		healthBar,
		TweenInfo.new(animationTime, animationStyle, animationDirection),
		{ Size = targetSize }
	)
	sizeTween:Play()

	-- Update health bar color with smooth transition
	local targetColor = UI_CONFIG.HEALTH_COLOR
	if health <= UI_CONFIG.LOW_HEALTH_THRESHOLD then
		targetColor = UI_CONFIG.LOW_HEALTH_COLOR
	end

	local colorTween = TweenService:Create(
		healthBar,
		TweenInfo.new(animationTime, animationStyle, animationDirection),
		{ BackgroundColor3 = targetColor }
	)
	colorTween:Play()

	-- Animate health text with fade effect
	local textTween = TweenService:Create(
		healthText,
		TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{ TextTransparency = 1 }
	)
	textTween:Play()

	-- Update text after fade out, then fade back in
	textTween.Completed:Connect(function()
		healthText.Text = health .. " / " .. maxHP
		local fadeInTween = TweenService:Create(
			healthText,
			TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{ TextTransparency = 0 }
		)
		fadeInTween:Play()
	end)

	-- Enhanced flash effect when taking damage
	if health < previousHealth then
		print("Damage detected - showing enhanced flash effect")

		-- Create flash overlay with rounded corners
		local flashFrame = Instance.new("Frame")
		flashFrame.Size = UDim2.new(1, 0, 1, 0)
		flashFrame.Position = UDim2.new(0, 0, 0, 0)
		flashFrame.BackgroundColor3 = Color3.fromRGB(255, 50, 50)
		flashFrame.BackgroundTransparency = 0.3
		flashFrame.BorderSizePixel = 0
		flashFrame.Parent = healthBar.Parent

		-- Add rounded corners to flash
		local flashCorner = Instance.new("UICorner")
		flashCorner.CornerRadius = UDim.new(0, 8)
		flashCorner.Parent = flashFrame

		-- Create pulsing effect
		local pulseIn = TweenService:Create(
			flashFrame,
			TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{ BackgroundTransparency = 0.1 }
		)

		local pulseOut = TweenService:Create(
			flashFrame,
			TweenInfo.new(0.6, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{ BackgroundTransparency = 1 }
		)

		-- Chain the animations
		pulseIn:Play()
		pulseIn.Completed:Connect(function()
			pulseOut:Play()
		end)

		-- Remove flash frame after animation
		pulseOut.Completed:Connect(function()
			flashFrame:Destroy()
		end)

		-- Add screen shake effect to the health bar
		local originalPosition = healthBar.Parent.Position
		local shakeIntensity = 0.02

		for i = 1, 3 do
			local shakeX = (math.random() - 0.5) * shakeIntensity
			local shakeY = (math.random() - 0.5) * shakeIntensity

			local shakeTween = TweenService:Create(
				healthBar.Parent,
				TweenInfo.new(0.05, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{ Position = originalPosition + UDim2.new(0, shakeX * 100, 0, shakeY * 100) }
			)
			shakeTween:Play()

			wait(0.05)
		end

		-- Return to original position
		local returnTween = TweenService:Create(
			healthBar.Parent,
			TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{ Position = originalPosition }
		)
		returnTween:Play()
	end

	print("Health UI updated successfully: " .. health .. "/" .. maxHP)
end

-- Function to handle player death
local function onPlayerDeath()
	print("Player died - updating UI")

	-- Create death text above character
	if billboardGui then
		local deathText = Instance.new("TextLabel")
		deathText.Name = "DeathText"
		deathText.Size = UDim2.new(1, 0, 0.4, 0)
		deathText.Position = UDim2.new(0, 0, -0.5, 0)
		deathText.BackgroundTransparency = 1
		deathText.Text = "DEAD"
		deathText.TextColor3 = Color3.fromRGB(255, 0, 0)
		deathText.TextScaled = true
		deathText.Font = Enum.Font.GothamBold
		deathText.TextStrokeTransparency = 0
		deathText.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
		deathText.Parent = billboardGui

		-- Remove death text after 3 seconds
		game:GetService("Debris"):AddItem(deathText, 3)
	end
end

-- Function to handle character spawning
local function onCharacterAdded(character)
	-- Wait for character to load
	character:WaitForChild("HumanoidRootPart", 10)
	wait(0.5)

	-- Create UI for new character
	createHealthUI()
end

-- Function to initialize the health UI system
function HealthUI.initialize()
	print("Initializing Health UI system...")

	-- Connect to server events
	updateHealthEvent.OnClientEvent:Connect(updateHealthDisplay)
	playerDeathEvent.OnClientEvent:Connect(onPlayerDeath)

	-- Handle character spawning
	if player.Character then
		onCharacterAdded(player.Character)
	end
	player.CharacterAdded:Connect(onCharacterAdded)

	print("Health UI system initialized")
end

-- Auto-initialize when script loads
HealthUI.initialize()

print("Client Health UI loaded - UI appears above character")

return HealthUI
