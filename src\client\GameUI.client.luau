-- Client-side Game UI System
-- Handles intro screen, ready system, and game start

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitF<PERSON><PERSON>hild("PlayerGui")

-- Wait for RemoteEvents
local gameEvents = ReplicatedStorage:WaitFor<PERSON>hild("GameEvents")
local updateGameStateEvent = gameEvents:WaitForChild("UpdateGameState")
local playerReadyEvent = gameEvents:WaitForChild("PlayerReady")
local startGameEvent = gameEvents:WaitForChild("StartGame")

-- UI Configuration
local UI_CONFIG = {
	BACKGROUND_COLOR = Color3.fromRGB(0, 0, 0), -- Black background
	TEXT_COLOR = Color3.fromRGB(255, 255, 255), -- White text
	BUTTON_COLOR = Color3.fromRGB(50, 150, 50), -- Green button
	BUTTON_HOVER_COLOR = Color3.fromRGB(70, 170, 70), -- Lighter green
	READY_BUTTON_COLOR = Color3.fromRGB(150, 50, 50), -- Red when ready
}

local GameUI = {}
local introGui = nil
local readyButton = nil
local statusLabel = nil
local isReady = false

-- Function to create the intro screen
local function createIntroScreen()
	-- Create ScreenGui
	introGui = Instance.new("ScreenGui")
	introGui.Name = "GameIntroUI"
	introGui.ResetOnSpawn = false
	introGui.Parent = playerGui
	
	-- Create black background
	local background = Instance.new("Frame")
	background.Name = "Background"
	background.Size = UDim2.new(1, 0, 1, 0)
	background.Position = UDim2.new(0, 0, 0, 0)
	background.BackgroundColor3 = UI_CONFIG.BACKGROUND_COLOR
	background.BorderSizePixel = 0
	background.Parent = introGui
	
	-- Create main container
	local mainContainer = Instance.new("Frame")
	mainContainer.Name = "MainContainer"
	mainContainer.Size = UDim2.new(0, 400, 0, 300)
	mainContainer.Position = UDim2.new(0.5, -200, 0.5, -150)
	mainContainer.BackgroundTransparency = 1
	mainContainer.Parent = background
	
	-- Create status label
	statusLabel = Instance.new("TextLabel")
	statusLabel.Name = "StatusLabel"
	statusLabel.Size = UDim2.new(1, 0, 0, 60)
	statusLabel.Position = UDim2.new(0, 0, 0, 0)
	statusLabel.BackgroundTransparency = 1
	statusLabel.Text = "Waiting for all players to connect..."
	statusLabel.TextColor3 = UI_CONFIG.TEXT_COLOR
	statusLabel.TextScaled = true
	statusLabel.Font = Enum.Font.GothamBold
	statusLabel.TextStrokeTransparency = 0
	statusLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
	statusLabel.Parent = mainContainer
	
	-- Create ready button (initially hidden)
	readyButton = Instance.new("TextButton")
	readyButton.Name = "ReadyButton"
	readyButton.Size = UDim2.new(0, 200, 0, 60)
	readyButton.Position = UDim2.new(0.5, -100, 0, 120)
	readyButton.BackgroundColor3 = UI_CONFIG.BUTTON_COLOR
	readyButton.Text = "READY UP"
	readyButton.TextColor3 = UI_CONFIG.TEXT_COLOR
	readyButton.TextScaled = true
	readyButton.Font = Enum.Font.GothamBold
	readyButton.BorderSizePixel = 0
	readyButton.Visible = false
	readyButton.Parent = mainContainer
	
	-- Add rounded corners to button
	local buttonCorner = Instance.new("UICorner")
	buttonCorner.CornerRadius = UDim.new(0, 10)
	buttonCorner.Parent = readyButton
	
	-- Create ready status label
	local readyStatusLabel = Instance.new("TextLabel")
	readyStatusLabel.Name = "ReadyStatusLabel"
	readyStatusLabel.Size = UDim2.new(1, 0, 0, 40)
	readyStatusLabel.Position = UDim2.new(0, 0, 0, 200)
	readyStatusLabel.BackgroundTransparency = 1
	readyStatusLabel.Text = "Ready: 0/0"
	readyStatusLabel.TextColor3 = UI_CONFIG.TEXT_COLOR
	readyStatusLabel.TextScaled = true
	readyStatusLabel.Font = Enum.Font.Gotham
	readyStatusLabel.Visible = false
	readyStatusLabel.Parent = mainContainer
	
	-- Button hover effects
	readyButton.MouseEnter:Connect(function()
		if not isReady then
			local hoverTween = TweenService:Create(
				readyButton,
				TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{BackgroundColor3 = UI_CONFIG.BUTTON_HOVER_COLOR}
			)
			hoverTween:Play()
		end
	end)
	
	readyButton.MouseLeave:Connect(function()
		if not isReady then
			local leaveTween = TweenService:Create(
				readyButton,
				TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{BackgroundColor3 = UI_CONFIG.BUTTON_COLOR}
			)
			leaveTween:Play()
		end
	end)
	
	-- Button click event
	readyButton.MouseButton1Click:Connect(function()
		if not isReady then
			isReady = true
			readyButton.Text = "READY!"
			readyButton.BackgroundColor3 = UI_CONFIG.READY_BUTTON_COLOR
			
			-- Send ready status to server
			playerReadyEvent:FireServer()
			
			print("Player marked as ready")
		end
	end)
	
	print("Intro screen created")
end

-- Function to update game state display
local function updateGameState(state, readyCount, totalPlayers)
	if not introGui or not statusLabel then return end
	
	print("Updating game state: " .. state .. ", Ready: " .. readyCount .. "/" .. totalPlayers)
	
	if state == "WAITING" then
		statusLabel.Text = "Waiting for all players to connect..."
		readyButton.Visible = false
		introGui:FindFirstChild("MainContainer"):FindFirstChild("ReadyStatusLabel").Visible = false
		
	elseif state == "READY_PHASE" then
		statusLabel.Text = "Get ready to start!"
		readyButton.Visible = true
		
		local readyStatusLabel = introGui:FindFirstChild("MainContainer"):FindFirstChild("ReadyStatusLabel")
		readyStatusLabel.Text = "Ready: " .. readyCount .. "/" .. totalPlayers
		readyStatusLabel.Visible = true
		
		-- Animate button appearance
		readyButton.Size = UDim2.new(0, 0, 0, 60)
		local buttonTween = TweenService:Create(
			readyButton,
			TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{Size = UDim2.new(0, 200, 0, 60)}
		)
		buttonTween:Play()
	end
end

-- Function to handle game start
local function onGameStart()
	print("Game starting - removing intro screen")
	
	if introGui then
		-- Fade out intro screen
		local fadeOutTween = TweenService:Create(
			introGui:FindFirstChild("Background"),
			TweenInfo.new(1.0, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{BackgroundTransparency = 1}
		)
		
		-- Fade out all text
		for _, child in pairs(introGui:GetDescendants()) do
			if child:IsA("TextLabel") or child:IsA("TextButton") then
				local textFadeTween = TweenService:Create(
					child,
					TweenInfo.new(1.0, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{TextTransparency = 1, BackgroundTransparency = 1}
				)
				textFadeTween:Play()
			end
		end
		
		fadeOutTween:Play()
		
		-- Remove intro screen after fade
		fadeOutTween.Completed:Connect(function()
			introGui:Destroy()
			introGui = nil
			print("Intro screen removed - game started!")
		end)
	end
end

-- Function to initialize the game UI system
function GameUI.initialize()
	print("Initializing Game UI system...")
	
	-- Create intro screen
	createIntroScreen()
	
	-- Connect to server events
	updateGameStateEvent.OnClientEvent:Connect(updateGameState)
	startGameEvent.OnClientEvent:Connect(onGameStart)
	
	print("Game UI system initialized")
end

-- Auto-initialize when script loads
GameUI.initialize()

print("Client Game UI loaded")

return GameUI
