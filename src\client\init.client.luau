print("Hello world, from client!")

-- Load loading screen (runs automatically)
-- Note: LoadingScreen is a LocalScript and will run automatically

-- Load the top-down camera (client-side only)
require(script.TopDownCamera)

-- Note: HealthUI is a LocalScript and will run automatically
-- Note: GameUI is a LocalScript and will run automatically (if needed)

-- Optional: Load camera controller for keyboard controls
-- Uncomment the line below if you want keyboard controls (C to toggle, Q/E for height)
-- require(script.CameraController)

print("Client-side systems loaded: Loading Screen, Top-down Camera, Health UI")
