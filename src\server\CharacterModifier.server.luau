-- Server-side Character Modifier
-- Handles character speed modification for all players

local Players = game:GetService("Players")

-- Configuration
local CONFIG = {
	SPEED_MULTIPLIER = 0.7, -- 70% of original speed
}

local CharacterModifier = {}

-- Function to modify character speed, disable jumping, and remove sounds
local function modifyCharacterSpeed(character)
	local humanoid = character:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid", 5)
	local humanoidRootPart = character:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart", 5)
	
	if humanoid then
		-- Store original speeds
		local originalWalkSpeed = humanoid.WalkSpeed
		
		-- Apply speed multiplier to walking only
		humanoid.WalkSpeed = originalWalkSpeed * CONFIG.SPEED_MULTIPLIER
		
		-- Completely disable jumping
		humanoid.JumpPower = 0
		humanoid.JumpHeight = 0
		
		-- Also disable jump through state changes
		humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, false)
		humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, false)
		
		print("Character speed modified to " .. (CONFIG.SPEED_MULTIPLIER * 100) .. "% for player: " .. character.Name)
		print("Jumping completely disabled for player: " .. character.Name)
	end
	
	-- Remove footstep sounds
	if humanoid<PERSON><PERSON><PERSON><PERSON> then
		-- Remove existing footstep sounds
		for _, child in pairs(humanoidRootPart:GetChildren()) do
			if child:IsA("Sound") and (child.Name == "Running" or child.Name == "Walking" or child.Name == "Climbing") then
				child:Destroy()
				print("Removed footstep sound: " .. child.Name .. " for player: " .. character.Name)
			end
		end
		
		-- Disable sound creation by setting volume to 0 for any new sounds
		local function onChildAdded(child)
			if child:IsA("Sound") and (child.Name == "Running" or child.Name == "Walking" or child.Name == "Climbing") then
				child.Volume = 0
				print("Muted footstep sound: " .. child.Name .. " for player: " .. character.Name)
			end
		end
		
		humanoidRootPart.ChildAdded:Connect(onChildAdded)
		print("Footstep sounds disabled for player: " .. character.Name)
	end
end

-- Function to handle player joining
local function onPlayerAdded(player)
	print("Player joined: " .. player.Name)
	
	-- Handle character spawning
	local function onCharacterAdded(character)
		print("Character added for player: " .. player.Name)
		
		-- Wait for character to fully load
		character:WaitForChild("Humanoid", 10)
		character:WaitForChild("HumanoidRootPart", 10)
		
		-- Wait a bit more for all parts to load
		wait(1)
		
		-- Modify character
		modifyCharacterSpeed(character)
		
		print("Character modifications complete for player: " .. player.Name)
	end
	
	-- Connect to character events
	if player.Character then
		onCharacterAdded(player.Character)
	end
	player.CharacterAdded:Connect(onCharacterAdded)
end

-- Connect to player events
Players.PlayerAdded:Connect(onPlayerAdded)

-- Handle players already in game
for _, player in pairs(Players:GetPlayers()) do
	onPlayerAdded(player)
end

print("Server Character Modifier loaded - affects all players")

return CharacterModifier
