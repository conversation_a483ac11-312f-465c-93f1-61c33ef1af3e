-- Server-side Block Spawner System
-- Spawns blocks that move toward all players

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local Debris = game:GetService("Debris")

-- Configuration
local CONFIG = {
	SPAWN_RATE = 2, -- Seconds between spawns per player
	BLOCK_SPEED = 8, -- Speed blocks move toward players
	BLOCK_SIZE = Vector3.new(3, 3, 3), -- Size of blocks
	BLOCK_COLOR = Color3.fromRGB(50, 255, 50), -- Bright green blocks
	BLOCK_MATERIAL = Enum.Material.Neon,
	SPAWN_DISTANCE = 50, -- Distance from player to spawn blocks
	DESPAWN_DISTANCE = 5, -- Distance to player when block despawns
	BLOCK_LIFETIME = 15, -- Max lifetime of blocks in seconds
	MAX_BLOCKS_PER_PLAYER = 10, -- Maximum blocks per player at once
}

local BlockSpawner = {}
local playerBlocks = {} -- Track blocks for each player
local lastSpawnTimes = {} -- Track last spawn time for each player
local updateConnection = nil

-- Function to get random spawn position around player
local function getRandomSpawnPosition(playerPosition)
	local angle = math.random() * math.pi * 2 -- Random angle
	local distance = CONFIG.SPAWN_DISTANCE

	local spawnX = playerPosition.X + math.cos(angle) * distance
	local spawnZ = playerPosition.Z + math.sin(angle) * distance
	local spawnY = playerPosition.Y + math.random(-5, 10) -- Slight height variation

	return Vector3.new(spawnX, spawnY, spawnZ)
end

-- Function to create a green attack block for a specific player
local function createBlock(position, targetPlayer)
	local block = Instance.new("Part")
	block.Name = "GreenAttackBlock_" .. targetPlayer.Name
	block.Size = CONFIG.BLOCK_SIZE
	block.Color = CONFIG.BLOCK_COLOR
	block.Material = CONFIG.BLOCK_MATERIAL
	block.Shape = Enum.PartType.Block
	block.CanCollide = false
	block.Anchored = true
	block.Position = position
	block.TopSurface = Enum.SurfaceType.Smooth
	block.BottomSurface = Enum.SurfaceType.Smooth

	-- Add glow effect
	local pointLight = Instance.new("PointLight")
	pointLight.Color = CONFIG.BLOCK_COLOR
	pointLight.Brightness = 1.0
	pointLight.Range = 8
	pointLight.Parent = block

	-- Add to workspace
	block.Parent = workspace

	-- Initialize player blocks table if needed
	if not playerBlocks[targetPlayer] then
		playerBlocks[targetPlayer] = {}
	end

	-- Add to player's blocks list
	table.insert(playerBlocks[targetPlayer], {
		block = block,
		startTime = tick(),
		targetPlayer = targetPlayer,
	})

	-- Auto-cleanup after lifetime
	Debris:AddItem(block, CONFIG.BLOCK_LIFETIME)

	print("Green attack block spawned for player: " .. targetPlayer.Name .. " at: " .. tostring(position))
	return block
end

-- Function to update block movement for all players
local function updateBlocks()
	for player, blocks in pairs(playerBlocks) do
		if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
			local playerPosition = player.Character.HumanoidRootPart.Position

			-- Update each block for this player
			for i = #blocks, 1, -1 do
				local blockData = blocks[i]
				local block = blockData.block

				if block and block.Parent then
					-- Calculate direction to player
					local currentPos = block.Position
					local direction = (playerPosition - currentPos).Unit

					-- Move block toward player
					local newPosition = currentPos + direction * CONFIG.BLOCK_SPEED * (1 / 60) -- 60 FPS
					block.Position = newPosition

					-- Check if block reached player (despawn distance)
					local distanceToPlayer = (newPosition - playerPosition).Magnitude
					if distanceToPlayer <= CONFIG.DESPAWN_DISTANCE then
						-- Block reached player - remove it
						block:Destroy()
						table.remove(blocks, i)
						print("Green block reached player " .. player.Name .. " and despawned")
					end
				else
					-- Block was destroyed, remove from list
					table.remove(blocks, i)
				end
			end
		end
	end
end

-- Function to spawn blocks for all players
local function spawnBlocks()
	local currentTime = tick()
	local playersInGame = Players:GetPlayers()

	-- Debug: Check if function is being called
	if #playersInGame > 0 and currentTime % 5 < 0.1 then -- Print every 5 seconds
		print("Spawn function called - Players:", #playersInGame, "Time:", currentTime)
	end

	for _, player in pairs(playersInGame) do
		if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
			-- Initialize last spawn time if needed
			if not lastSpawnTimes[player] then
				lastSpawnTimes[player] = 0
			end

			-- Check spawn rate for this player
			if currentTime - lastSpawnTimes[player] < CONFIG.SPAWN_RATE then
				continue
			end

			-- Initialize player blocks if needed
			if not playerBlocks[player] then
				playerBlocks[player] = {}
			end

			-- Check max blocks limit for this player
			if #playerBlocks[player] >= CONFIG.MAX_BLOCKS_PER_PLAYER then
				print("Max blocks reached for player:", player.Name, #playerBlocks[player])
				continue
			end

			local playerPosition = player.Character.HumanoidRootPart.Position
			local spawnPosition = getRandomSpawnPosition(playerPosition)

			-- Create block for this player
			print("Creating block for player:", player.Name, "at position:", spawnPosition)
			createBlock(spawnPosition, player)
			lastSpawnTimes[player] = currentTime
		end
	end
end

-- Function to start the block spawner
function BlockSpawner.start()
	if updateConnection then
		print("Block spawner already running")
		return
	end

	print("Starting server-side block spawner system...")
	print("Current players in game:", #Players:GetPlayers())

	-- Connect update function for spawning and movement
	updateConnection = RunService.Heartbeat:Connect(function()
		spawnBlocks()
		updateBlocks()
	end)

	print("Block spawner started for all players!")
	print("Blocks will spawn every " .. CONFIG.SPAWN_RATE .. " seconds per player")
	print("Blocks move at speed: " .. CONFIG.BLOCK_SPEED)
	print("Block spawner connection established:", updateConnection ~= nil)
end

-- Function to stop the block spawner
function BlockSpawner.stop()
	if updateConnection then
		updateConnection:Disconnect()
		updateConnection = nil
	end

	-- Clean up existing blocks
	for player, blocks in pairs(playerBlocks) do
		for _, blockData in pairs(blocks) do
			if blockData.block and blockData.block.Parent then
				blockData.block:Destroy()
			end
		end
	end
	playerBlocks = {}
	lastSpawnTimes = {}

	print("Block spawner stopped and cleaned up")
end

-- Handle player leaving
local function onPlayerRemoving(player)
	-- Clean up blocks for leaving player
	if playerBlocks[player] then
		for _, blockData in pairs(playerBlocks[player]) do
			if blockData.block and blockData.block.Parent then
				blockData.block:Destroy()
			end
		end
		playerBlocks[player] = nil
	end

	-- Clean up spawn time tracking
	lastSpawnTimes[player] = nil

	print("Cleaned up blocks for leaving player: " .. player.Name)
end

-- Connect to player events
Players.PlayerRemoving:Connect(onPlayerRemoving)

-- Note: BlockSpawner will be started by GameManager when game begins
-- Do not auto-start here

print("Server Block Spawner system loaded (will be started by GameManager)")

return BlockSpawner
