-- Server-side Game Manager
-- Handles game states, player readiness, and game start

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Configuration
local CONFIG = {
	MIN_PLAYERS = 1, -- Minimum players to start game
	WAITING_TIME = 2, -- Reduced to 2 seconds for testing
	AUTO_START_FOR_TESTING = true, -- Auto-start game for testing
}

-- Create RemoteEvents for client communication
local remoteEvents = Instance.new("Folder")
remoteEvents.Name = "GameEvents"
remoteEvents.Parent = ReplicatedStorage

local updateGameStateEvent = Instance.new("RemoteEvent")
updateGameStateEvent.Name = "UpdateGameState"
updateGameStateEvent.Parent = remoteEvents

local playerReadyEvent = Instance.new("RemoteEvent")
playerReadyEvent.Name = "PlayerReady"
playerReadyEvent.Parent = remoteEvents

local startGameEvent = Instance.new("RemoteEvent")
startGameEvent.Name = "StartGame"
startGameEvent.Parent = remoteEvents

-- Game state tracking
local gameState = "WAITING" -- WAITING, READY_PHASE, PLAYING
local readyPlayers = {}
local allPlayers = {}

local GameManager = {}

-- Function to update all clients with current game state
local function updateAllClients()
	local totalPlayers = #allPlayers
	local readyCount = 0

	for _, player in pairs(allPlayers) do
		if readyPlayers[player] then
			readyCount = readyCount + 1
		end
	end

	-- Send update to all players
	for _, player in pairs(allPlayers) do
		updateGameStateEvent:FireClient(player, gameState, readyCount, totalPlayers)
	end

	print("Game state update sent - State: " .. gameState .. ", Ready: " .. readyCount .. "/" .. totalPlayers)
end

-- Function to check if all players are ready
local function checkAllPlayersReady()
	if gameState ~= "READY_PHASE" then
		return
	end

	local totalPlayers = #allPlayers
	local readyCount = 0

	for _, player in pairs(allPlayers) do
		if readyPlayers[player] then
			readyCount = readyCount + 1
		end
	end

	-- Check if all players are ready
	if readyCount >= totalPlayers and totalPlayers >= CONFIG.MIN_PLAYERS then
		startGame()
	end
end

-- Function to start the game
function startGame()
	print("Starting game! All players are ready.")
	gameState = "PLAYING"

	-- Clear ready status
	readyPlayers = {}

	-- Notify all clients that game is starting
	for _, player in pairs(allPlayers) do
		startGameEvent:FireClient(player)
	end

	-- Start game systems
	print("Attempting to start block spawner...")

	-- Get the BlockSpawner module
	local success, blockSpawner = pcall(function()
		return require(script.Parent.BlockSpawner)
	end)

	if success and blockSpawner and blockSpawner.start then
		blockSpawner.start()
		print("Block spawner started successfully!")
	else
		print("Failed to start block spawner:", blockSpawner)
	end

	print("Game started! Block spawning enabled.")
end

-- Function to handle player ready status
local function onPlayerReady(player)
	if gameState ~= "READY_PHASE" then
		return
	end

	readyPlayers[player] = true
	print("Player " .. player.Name .. " is ready!")

	-- Update all clients
	updateAllClients()

	-- Check if all players are ready
	checkAllPlayersReady()
end

-- Function to handle player joining
local function onPlayerAdded(player)
	print("Player joined: " .. player.Name)

	-- Add to player list
	table.insert(allPlayers, player)

	-- If this is the first player, start waiting phase
	if #allPlayers == 1 then
		gameState = "WAITING"
		print("First player joined - starting waiting phase")

		-- Wait for more players or timeout
		wait(CONFIG.WAITING_TIME)

		-- Move to ready phase
		gameState = "READY_PHASE"
		print("Moving to ready phase")

		-- Auto-start for testing if enabled
		if CONFIG.AUTO_START_FOR_TESTING then
			print("Auto-starting game for testing...")
			wait(1) -- Brief delay
			startGame()
		end
	end

	-- Update all clients
	updateAllClients()
end

-- Function to handle player leaving
local function onPlayerRemoving(player)
	print("Player leaving: " .. player.Name)

	-- Remove from player list
	for i, p in pairs(allPlayers) do
		if p == player then
			table.remove(allPlayers, i)
			break
		end
	end

	-- Remove from ready list
	readyPlayers[player] = nil

	-- If no players left, reset game
	if #allPlayers == 0 then
		gameState = "WAITING"
		readyPlayers = {}
		print("No players left - resetting to waiting state")

		-- Stop block spawner
		local blockSpawner = require(script.Parent.BlockSpawner)
		if blockSpawner and blockSpawner.stop then
			blockSpawner.stop()
		end
	else
		-- Update remaining clients
		updateAllClients()

		-- Check if game should continue
		if gameState == "PLAYING" and #allPlayers < CONFIG.MIN_PLAYERS then
			-- Not enough players, go back to ready phase
			gameState = "READY_PHASE"
			readyPlayers = {}
			print("Not enough players - returning to ready phase")
			updateAllClients()
		end
	end
end

-- Function to initialize the game manager
function GameManager.initialize()
	print("Initializing Game Manager...")

	-- Connect player events
	Players.PlayerAdded:Connect(onPlayerAdded)
	Players.PlayerRemoving:Connect(onPlayerRemoving)

	-- Connect ready event
	playerReadyEvent.OnServerEvent:Connect(onPlayerReady)

	-- Handle existing players
	for _, player in pairs(Players:GetPlayers()) do
		onPlayerAdded(player)
	end

	print("Game Manager initialized!")
	print("Min players to start: " .. CONFIG.MIN_PLAYERS)
	print("Waiting time: " .. CONFIG.WAITING_TIME .. " seconds")
end

-- Auto-initialize
GameManager.initialize()

print("Server Game Manager loaded")

return GameManager
