-- Optional Camera Controller
-- This script demonstrates how to control the TopDownCamera with user input
-- You can require this in init.client.luau if you want keyboard controls

local UserInputService = game:GetService("UserInputService")
local TopDownCamera = require(script.Parent.TopDownCamera)

local CameraController = {}

-- Key bindings
local TOGGLE_KEY = Enum.KeyCode.C  -- Press C to toggle camera
local HEIGHT_UP_KEY = Enum.KeyCode.Q  -- Press Q to increase height
local HEIGHT_DOWN_KEY = Enum.KeyCode.E  -- Press E to decrease height

-- State
local isTopDownEnabled = true

-- Input handling
local function onKeyPressed(key, gameProcessed)
    if gameProcessed then return end
    
    if key.KeyCode == TOGGLE_KEY then
        if isTopDownEnabled then
            TopDownCamera.disable()
            isTopDownEnabled = false
            print("Switched to default camera (Press C to toggle back)")
        else
            TopDownCamera.enable()
            isTopDownEnabled = true
            print("Switched to top-down camera (Press C to toggle)")
        end
        
    elseif key.KeyCode == HEIGHT_UP_KEY and isTopDownEnabled then
        -- Increase camera height
        TopDownCamera.setHeight(25 + 5) -- You might want to track current height
        print("Camera height increased (Press Q/E to adjust)")
        
    elseif key.KeyCode == HEIGHT_DOWN_KEY and isTopDownEnabled then
        -- Decrease camera height
        TopDownCamera.setHeight(25 - 5) -- You might want to track current height
        print("Camera height decreased (Press Q/E to adjust)")
    end
end

-- Connect input
UserInputService.InputBegan:Connect(onKeyPressed)

-- Instructions
print("=== Top-Down Camera Controls ===")
print("C - Toggle between top-down and default camera")
print("Q - Increase camera height")
print("E - Decrease camera height")
print("================================")

return CameraController
