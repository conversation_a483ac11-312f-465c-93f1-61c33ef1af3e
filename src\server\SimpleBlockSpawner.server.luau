-- Simple Block Spawner System
-- Creates green blocks that move toward players

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local Debris = game:GetService("Debris")

-- Configuration
local CONFIG = {
	MAX_GREEN_BLOCKS = 3, -- Only 3 green blocks at a time
	GREEN_BLOCK_SPEED = 4, -- Green blocks move slower
	RED_BLOCK_SPEED = 2, -- Red blocks move very slow
	GREEN_BLOCK_SIZE = Vector3.new(1.5, 1.5, 1.5), -- Smaller green blocks
	RED_BLOCK_SIZE = Vector3.new(3, 3, 3), -- Normal sized red blocks
	GREEN_BLOCK_COLOR = Color3.fromRGB(0, 255, 0), -- Bright green
	RED_BLOCK_COLOR = Color3.fromRGB(255, 0, 0), -- Bright red
	SPAWN_DISTANCE = 40, -- Distance from player to spawn blocks
	BLOCK_LIFETIME = 30, -- Max lifetime of blocks
	RESPAWN_DELAY = 1, -- Seconds before respawning a new block
}

local SimpleBlockSpawner = {}
local isRunning = false
local spawnConnection = nil
local moveConnection = nil
local allBlocks = {}

-- Function to get random spawn position around a player
local function getRandomSpawnPosition(playerPosition)
	local angle = math.random() * math.pi * 2
	local distance = CONFIG.SPAWN_DISTANCE

	local x = playerPosition.X + math.cos(angle) * distance
	local z = playerPosition.Z + math.sin(angle) * distance
	local y = playerPosition.Y + math.random(-3, 8)

	return Vector3.new(x, y, z)
end

-- Function to create a green block
local function createGreenBlock(position, targetPlayer)
	local block = Instance.new("Part")
	block.Name = "GreenAttackBlock"
	block.Size = CONFIG.GREEN_BLOCK_SIZE
	block.Color = CONFIG.GREEN_BLOCK_COLOR
	block.Material = Enum.Material.Neon
	block.Shape = Enum.PartType.Block
	block.CanCollide = false
	block.Anchored = true
	block.Position = position
	block.TopSurface = Enum.SurfaceType.Smooth
	block.BottomSurface = Enum.SurfaceType.Smooth

	-- Add glow effect
	local light = Instance.new("PointLight")
	light.Color = CONFIG.GREEN_BLOCK_COLOR
	light.Brightness = 1.5
	light.Range = 12
	light.Parent = block

	-- Add to workspace
	block.Parent = workspace

	-- Track the block
	table.insert(allBlocks, {
		block = block,
		target = targetPlayer,
		created = tick(),
		blockType = "green",
		speed = CONFIG.GREEN_BLOCK_SPEED,
	})

	-- Auto-cleanup
	Debris:AddItem(block, CONFIG.BLOCK_LIFETIME)

	print("Green block created at:", position, "targeting:", targetPlayer.Name)
	return block
end

-- Function to create a red block (instant kill)
local function createRedBlock(position, targetPlayer)
	local block = Instance.new("Part")
	block.Name = "RedAttackBlock"
	block.Size = CONFIG.RED_BLOCK_SIZE
	block.Color = CONFIG.RED_BLOCK_COLOR
	block.Material = Enum.Material.Neon
	block.Shape = Enum.PartType.Block
	block.CanCollide = false
	block.Anchored = true
	block.Position = position
	block.TopSurface = Enum.SurfaceType.Smooth
	block.BottomSurface = Enum.SurfaceType.Smooth

	-- Add menacing glow effect
	local light = Instance.new("PointLight")
	light.Color = CONFIG.RED_BLOCK_COLOR
	light.Brightness = 2.0
	light.Range = 15
	light.Parent = block

	-- Add pulsing effect for danger
	local surfaceLight = Instance.new("SurfaceLight")
	surfaceLight.Color = CONFIG.RED_BLOCK_COLOR
	surfaceLight.Brightness = 1.5
	surfaceLight.Face = Enum.NormalId.Top
	surfaceLight.Parent = block

	-- Add to workspace
	block.Parent = workspace

	-- Track the block
	table.insert(allBlocks, {
		block = block,
		target = targetPlayer,
		created = tick(),
		blockType = "red",
		speed = CONFIG.RED_BLOCK_SPEED,
	})

	-- Auto-cleanup
	Debris:AddItem(block, CONFIG.BLOCK_LIFETIME)

	print("RED DANGER BLOCK created at:", position, "targeting:", targetPlayer.Name)
	return block
end

-- Function to move all blocks toward their targets
local function moveBlocks()
	for i = #allBlocks, 1, -1 do
		local blockData = allBlocks[i]
		local block = blockData.block
		local targetPlayer = blockData.target

		-- Check if block still exists
		if not block or not block.Parent then
			table.remove(allBlocks, i)
			continue
		end

		-- Check if target player still exists and has character
		if not targetPlayer or not targetPlayer.Parent or not targetPlayer.Character then
			block:Destroy()
			table.remove(allBlocks, i)
			continue
		end

		local humanoidRootPart = targetPlayer.Character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			continue
		end

		-- Calculate movement
		local blockPosition = block.Position
		local targetPosition = humanoidRootPart.Position
		local direction = (targetPosition - blockPosition).Unit
		local distance = (targetPosition - blockPosition).Magnitude

		-- Move block toward target using individual speed
		local blockSpeed = blockData.speed or CONFIG.GREEN_BLOCK_SPEED
		local newPosition = blockPosition + direction * blockSpeed * (1 / 60)
		block.Position = newPosition

		-- Remove block if it gets too close (will be handled by health system)
		if distance <= 3 then
			table.remove(allBlocks, i)
			-- Don't destroy here - let health system handle it
		end
	end
end

-- Function to count green blocks for a player
local function countGreenBlocks(targetPlayer)
	local count = 0
	for _, blockData in pairs(allBlocks) do
		if blockData.target == targetPlayer and blockData.blockType == "green" then
			count = count + 1
		end
	end
	return count
end

-- Function to maintain green block count
local function maintainGreenBlocks()
	local players = Players:GetPlayers()

	for _, player in pairs(players) do
		if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
			local currentGreenBlocks = countGreenBlocks(player)
			local blocksNeeded = CONFIG.MAX_GREEN_BLOCKS - currentGreenBlocks

			-- Spawn missing green blocks
			for i = 1, blocksNeeded do
				local playerPosition = player.Character.HumanoidRootPart.Position
				local spawnPosition = getRandomSpawnPosition(playerPosition)
				createGreenBlock(spawnPosition, player)
			end
		end
	end
end

-- Function to start the simple block spawner
function SimpleBlockSpawner.start()
	if isRunning then
		print("Simple block spawner already running")
		return
	end

	isRunning = true
	allBlocks = {}

	print("Starting Simple Block Spawner...")
	print("Players in game:", #Players:GetPlayers())

	-- Maintain green block count periodically
	spawnConnection = task.spawn(function()
		while isRunning do
			maintainGreenBlocks()
			task.wait(CONFIG.RESPAWN_DELAY)
		end
	end)

	-- Move blocks continuously
	moveConnection = RunService.Heartbeat:Connect(moveBlocks)

	print("Simple Block Spawner started!")
	print("Maintaining", CONFIG.MAX_GREEN_BLOCKS, "green blocks per player")
	print("Green block speed:", CONFIG.GREEN_BLOCK_SPEED)
	print("Red block speed:", CONFIG.RED_BLOCK_SPEED)

	-- Spawn first batch immediately
	maintainGreenBlocks()
end

-- Function to stop the simple block spawner
function SimpleBlockSpawner.stop()
	if not isRunning then
		return
	end

	isRunning = false

	-- Stop spawning
	if spawnConnection then
		task.cancel(spawnConnection)
		spawnConnection = nil
	end

	-- Stop movement
	if moveConnection then
		moveConnection:Disconnect()
		moveConnection = nil
	end

	-- Clean up all blocks
	for _, blockData in pairs(allBlocks) do
		if blockData.block and blockData.block.Parent then
			blockData.block:Destroy()
		end
	end
	allBlocks = {}

	print("Simple Block Spawner stopped and cleaned up")
end

-- Auto-start for testing
task.wait(5) -- Wait 5 seconds after server starts
SimpleBlockSpawner.start()

print("Simple Block Spawner loaded and will auto-start")

return SimpleBlockSpawner
