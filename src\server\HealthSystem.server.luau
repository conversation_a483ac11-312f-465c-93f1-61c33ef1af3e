-- Server-side Health System
-- Manages player health and damage from blocks

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Configuration
local CONFIG = {
	MAX_HEALTH = 100,
	BLOCK_DAMAGE = 5,
	DAMAGE_RATE = 1.0, -- Damage every 1 second while touching (slower)
	RESPAWN_DELAY = 15, -- 15 seconds before respawn
	TOUCH_DISTANCE = 5, -- Distance for block to be "touching" player
	DAMAGE_COOLDOWN = 1.5, -- Minimum time between damage from same block
	SPAWN_AREA_SIZE = 100, -- Size of random spawn area
}

-- Create RemoteEvents for client communication
local remoteEvents = Instance.new("Folder")
remoteEvents.Name = "HealthEvents"
remoteEvents.Parent = ReplicatedStorage

local updateHealthEvent = Instance.new("RemoteEvent")
updateHealthEvent.Name = "UpdateHealth"
updateHealthEvent.Parent = remoteEvents

local playerDeathEvent = Instance.new("RemoteEvent")
playerDeathEvent.Name = "PlayerDeath"
playerDeathEvent.Parent = remoteEvents

-- Player health tracking
local playerHealths = {}
local playerConnections = {}
local playerLastDamageTime = {} -- Track when each player was last damaged by each block

local HealthSystem = {}

-- Function to get random spawn position
local function getRandomSpawnPosition()
	local x = math.random(-CONFIG.SPAWN_AREA_SIZE, CONFIG.SPAWN_AREA_SIZE)
	local z = math.random(-CONFIG.SPAWN_AREA_SIZE, CONFIG.SPAWN_AREA_SIZE)
	local y = 10 -- Safe height above ground
	return Vector3.new(x, y, z)
end

-- Function to handle player death
local function handlePlayerDeath(player)
	print("Player " .. player.Name .. " died!")

	-- Notify client of death
	playerDeathEvent:FireClient(player)

	-- Disable character temporarily
	if player.Character and player.Character:FindFirstChild("Humanoid") then
		player.Character.Humanoid.PlatformStand = true

		-- Hide character
		for _, part in pairs(player.Character:GetChildren()) do
			if part:IsA("BasePart") and part.Name ~= "HumanoidRootPart" then
				part.Transparency = 1
			end
		end
	end

	-- Start respawn countdown
	task.spawn(function()
		for i = CONFIG.RESPAWN_DELAY, 1, -1 do
			-- Send countdown to client
			updateHealthEvent:FireClient(player, 0, CONFIG.MAX_HEALTH, "Respawning in " .. i .. "...")
			task.wait(1)
		end

		-- Respawn at random location
		if player.Character then
			local spawnPosition = getRandomSpawnPosition()

			-- Create new spawn location
			local spawnPart = Instance.new("SpawnLocation")
			spawnPart.Name = "TempSpawn_" .. player.Name
			spawnPart.Size = Vector3.new(6, 1, 6)
			spawnPart.Position = spawnPosition
			spawnPart.Anchored = true
			spawnPart.CanCollide = true
			spawnPart.BrickColor = BrickColor.new("Bright green")
			spawnPart.Parent = workspace

			-- Respawn player
			player:LoadCharacter()

			-- Remove temp spawn after respawn
			task.wait(2)
			if spawnPart and spawnPart.Parent then
				spawnPart:Destroy()
			end

			print("Player " .. player.Name .. " respawned at random location:", spawnPosition)
		end
	end)
end

-- Function to set player health
local function setPlayerHealth(player, health)
	health = math.max(0, math.min(CONFIG.MAX_HEALTH, health))
	playerHealths[player] = health

	print("Setting player " .. player.Name .. " health to: " .. health .. "/" .. CONFIG.MAX_HEALTH)

	-- Update client UI
	updateHealthEvent:FireClient(player, health, CONFIG.MAX_HEALTH)
	print("Sent health update to client for player: " .. player.Name)

	-- Check for death
	if health <= 0 then
		handlePlayerDeath(player)
	end
end

-- Function to damage player
local function damagePlayer(player, damage)
	if not playerHealths[player] then
		return
	end

	local currentHealth = playerHealths[player]
	local newHealth = currentHealth - damage

	setPlayerHealth(player, newHealth)
	print("Player " .. player.Name .. " took " .. damage .. " damage")
end

-- Function to check block collisions and deal continuous damage
local function checkBlockCollisions()
	local currentTime = tick()

	for player, health in pairs(playerHealths) do
		if player.Character and player.Character:FindFirstChild("HumanoidRootPart") and health > 0 then
			local playerPosition = player.Character.HumanoidRootPart.Position

			-- Initialize damage tracking for player if needed
			if not playerLastDamageTime[player] then
				playerLastDamageTime[player] = {}
			end

			-- Check all blocks in workspace
			for _, obj in pairs(workspace:GetChildren()) do
				if
					(
						obj.Name:match("AttackBlock")
						or obj.Name:match("GreenAttackBlock")
						or obj.Name:match("RedAttackBlock")
					) and obj:IsA("BasePart")
				then
					local distance = (obj.Position - playerPosition).Magnitude

					-- Check if block is touching player
					if distance <= CONFIG.TOUCH_DISTANCE then
						local blockId = tostring(obj)

						-- Check if this is a red block (instant kill)
						if obj.Name:match("RedAttackBlock") then
							-- Red block = instant death
							setPlayerHealth(player, 0)
							obj:Destroy()
							print("RED BLOCK hit player " .. player.Name .. " - INSTANT KILL!")

						-- Green block (5 damage)
						elseif obj.Name:match("GreenAttackBlock") then
							-- Check if enough time has passed since last damage from this block
							if
								not playerLastDamageTime[player][blockId]
								or (currentTime - playerLastDamageTime[player][blockId]) >= CONFIG.DAMAGE_COOLDOWN
							then
								-- Deal damage (only 5 damage per hit)
								damagePlayer(player, CONFIG.BLOCK_DAMAGE)
								playerLastDamageTime[player][blockId] = currentTime

								-- Destroy the block after dealing damage
								obj:Destroy()

								print(
									"Green block hit player "
										.. player.Name
										.. " - dealt "
										.. CONFIG.BLOCK_DAMAGE
										.. " damage (Health: "
										.. (playerHealths[player] or 0)
										.. ")"
								)
							end
						end
					else
						-- Block is not touching, remove from damage tracking
						local blockId = tostring(obj)
						if playerLastDamageTime[player][blockId] then
							playerLastDamageTime[player][blockId] = nil
							print("Green block stopped touching player " .. player.Name)
						end
					end
				end
			end
		end
	end
end

-- Function to initialize player health
local function initializePlayerHealth(player)
	playerHealths[player] = CONFIG.MAX_HEALTH

	-- Send initial health to client
	updateHealthEvent:FireClient(player, CONFIG.MAX_HEALTH, CONFIG.MAX_HEALTH)

	print(
		"Initialized health for player: " .. player.Name .. " (" .. CONFIG.MAX_HEALTH .. "/" .. CONFIG.MAX_HEALTH .. ")"
	)
end

-- Function to handle player joining
local function onPlayerAdded(player)
	print("Setting up health system for player: " .. player.Name)

	-- Initialize health when character spawns
	local function onCharacterAdded(character)
		-- Wait for character to load
		character:WaitForChild("Humanoid", 10)
		character:WaitForChild("HumanoidRootPart", 10)
		wait(1)

		-- Initialize health
		initializePlayerHealth(player)

		print("Health system ready for player: " .. player.Name)
	end

	-- Connect to character events
	if player.Character then
		onCharacterAdded(player.Character)
	end
	player.CharacterAdded:Connect(onCharacterAdded)
end

-- Function to handle player leaving
local function onPlayerRemoving(player)
	-- Clean up player data
	playerHealths[player] = nil
	playerLastDamageTime[player] = nil

	if playerConnections[player] then
		for _, connection in pairs(playerConnections[player]) do
			connection:Disconnect()
		end
		playerConnections[player] = nil
	end

	print("Cleaned up health system for player: " .. player.Name)
end

-- Start the health system
function HealthSystem.start()
	print("Starting server-side health system...")

	-- Connect collision checking
	RunService.Heartbeat:Connect(checkBlockCollisions)

	-- Connect player events
	Players.PlayerAdded:Connect(onPlayerAdded)
	Players.PlayerRemoving:Connect(onPlayerRemoving)

	-- Handle existing players
	for _, player in pairs(Players:GetPlayers()) do
		onPlayerAdded(player)
	end

	print("Health system started!")
	print("Max Health: " .. CONFIG.MAX_HEALTH)
	print("Block Damage: " .. CONFIG.BLOCK_DAMAGE)
	print("Respawn Delay: " .. CONFIG.RESPAWN_DELAY .. " seconds")
end

-- Healing function (for future use)
function HealthSystem.healPlayer(player, amount)
	if playerHealths[player] then
		local currentHealth = playerHealths[player]
		local newHealth = math.min(CONFIG.MAX_HEALTH, currentHealth + amount)
		setPlayerHealth(player, newHealth)
		print("Player " .. player.Name .. " healed for " .. amount .. " health")
	end
end

-- Get player health function
function HealthSystem.getPlayerHealth(player)
	return playerHealths[player] or 0
end

-- Auto-start the system
HealthSystem.start()

print("Server Health System loaded and started")

return HealthSystem
