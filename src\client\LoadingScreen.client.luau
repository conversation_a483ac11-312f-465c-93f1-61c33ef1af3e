-- Loading Screen with animated particles and loading bar
-- Shows "PLAY!" with blue background and dark blue particles

local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("PlayerGui")

-- Configuration
local CONFIG = {
	BACKGROUND_COLOR = Color3.fromRGB(15, 50, 120), -- Darker blue background
	PARTICLE_COLOR = Color3.fromRGB(5, 25, 80), -- Darker blue particles
	LOADING_BAR_COLOR = Color3.fromRGB(255, 255, 255), -- White loading bar
	TEXT_COLOR = Color3.fromRGB(255, 255, 255), -- White text
	PARTICLE_COUNT = 35, -- More particles
	LOADING_TIME = 3, -- Seconds to complete loading
}

local LoadingScreen = {}
local loadingGui = nil
local particles = {}
local loadingBar = nil
local isLoading = true

-- Function to create a particle
local function createParticle(container)
	local particle = Instance.new("Frame")
	particle.Size = UDim2.new(0, math.random(3, 8), 0, math.random(3, 8))
	particle.BackgroundColor3 = CONFIG.PARTICLE_COLOR
	particle.BorderSizePixel = 0
	particle.Position = UDim2.new(math.random(), 0, math.random(), 0)
	particle.Parent = container

	-- Add rounded corners
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(1, 0) -- Circular
	corner.Parent = particle

	return particle
end

-- Function to create the loading screen
local function createLoadingScreen()
	-- Create ScreenGui
	loadingGui = Instance.new("ScreenGui")
	loadingGui.Name = "LoadingScreen"
	loadingGui.ResetOnSpawn = false
	loadingGui.Parent = playerGui

	-- Create full screen background
	local background = Instance.new("Frame")
	background.Name = "Background"
	background.Size = UDim2.new(1, 0, 1, 0)
	background.Position = UDim2.new(0, 0, 0, 0)
	background.BackgroundColor3 = CONFIG.BACKGROUND_COLOR
	background.BorderSizePixel = 0
	background.Parent = loadingGui

	-- Create particles container
	local particleContainer = Instance.new("Frame")
	particleContainer.Name = "ParticleContainer"
	particleContainer.Size = UDim2.new(1, 0, 1, 0)
	particleContainer.Position = UDim2.new(0, 0, 0, 0)
	particleContainer.BackgroundTransparency = 1
	particleContainer.Parent = background

	-- Create particles
	for i = 1, CONFIG.PARTICLE_COUNT do
		local particle = createParticle(particleContainer)
		table.insert(particles, particle)
	end

	-- Create main container
	local mainContainer = Instance.new("Frame")
	mainContainer.Name = "MainContainer"
	mainContainer.Size = UDim2.new(0, 600, 0, 200)
	mainContainer.Position = UDim2.new(0.5, -300, 0.5, -100)
	mainContainer.BackgroundTransparency = 1
	mainContainer.Parent = background

	-- Create "PLAY!" button
	local playButton = Instance.new("TextButton")
	playButton.Name = "PlayButton"
	playButton.Size = UDim2.new(1, 0, 0, 80)
	playButton.Position = UDim2.new(0, 0, 0, 0)
	playButton.BackgroundTransparency = 1
	playButton.Text = "PLAY!"
	playButton.TextColor3 = CONFIG.TEXT_COLOR
	playButton.TextScaled = true
	playButton.Font = Enum.Font.GothamBold
	playButton.TextStrokeTransparency = 0
	playButton.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
	playButton.Parent = mainContainer

	-- Add hover effects to the button
	playButton.MouseEnter:Connect(function()
		print("🎮 Mouse entered PLAY! button")
		local hoverTween =
			TweenService:Create(playButton, TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				TextColor3 = Color3.fromRGB(255, 255, 100), -- Yellow on hover
				BackgroundTransparency = 0.8, -- Slight background
				BackgroundColor3 = Color3.fromRGB(255, 255, 255), -- White background
			})
		hoverTween:Play()
	end)

	playButton.MouseLeave:Connect(function()
		print("🎮 Mouse left PLAY! button")
		local leaveTween =
			TweenService:Create(playButton, TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				TextColor3 = CONFIG.TEXT_COLOR, -- Back to white
				BackgroundTransparency = 1, -- Remove background
			})
		leaveTween:Play()
	end)

	-- Button click event to remove loading screen
	playButton.MouseButton1Click:Connect(function()
		print("🎮 PLAY! button clicked!")
		print("🎮 Current isLoading state:", isLoading)
		print("🎮 LoadingGui exists:", loadingGui ~= nil)
		removeLoadingScreen()
	end)

	-- Create loading bar background
	local loadingBarBG = Instance.new("Frame")
	loadingBarBG.Name = "LoadingBarBackground"
	loadingBarBG.Size = UDim2.new(1, 0, 0, 20)
	loadingBarBG.Position = UDim2.new(0, 0, 0, 120)
	loadingBarBG.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
	loadingBarBG.BorderSizePixel = 0
	loadingBarBG.Parent = mainContainer

	-- Add rounded corners to loading bar background
	local bgCorner = Instance.new("UICorner")
	bgCorner.CornerRadius = UDim.new(0, 10)
	bgCorner.Parent = loadingBarBG

	-- Add outline to loading bar background
	local bgStroke = Instance.new("UIStroke")
	bgStroke.Color = CONFIG.TEXT_COLOR
	bgStroke.Thickness = 2
	bgStroke.Parent = loadingBarBG

	-- Create loading bar fill
	loadingBar = Instance.new("Frame")
	loadingBar.Name = "LoadingBar"
	loadingBar.Size = UDim2.new(0, 0, 1, 0)
	loadingBar.Position = UDim2.new(0, 0, 0, 0)
	loadingBar.BackgroundColor3 = CONFIG.LOADING_BAR_COLOR
	loadingBar.BorderSizePixel = 0
	loadingBar.Parent = loadingBarBG

	-- Add rounded corners to loading bar
	local barCorner = Instance.new("UICorner")
	barCorner.CornerRadius = UDim.new(0, 8)
	barCorner.Parent = loadingBar

	-- Create instruction text
	local instructionText = Instance.new("TextLabel")
	instructionText.Name = "InstructionText"
	instructionText.Size = UDim2.new(1, 0, 0, 30)
	instructionText.Position = UDim2.new(0, 0, 0, 160)
	instructionText.BackgroundTransparency = 1
	instructionText.Text = "Click PLAY! to start"
	instructionText.TextColor3 = CONFIG.TEXT_COLOR
	instructionText.TextScaled = true
	instructionText.Font = Enum.Font.Gotham
	instructionText.Parent = mainContainer

	print("Loading screen created")
end

-- Function to animate loading bar
local function animateLoadingBar()
	if not loadingBar then
		return
	end

	local tween = TweenService:Create(
		loadingBar,
		TweenInfo.new(CONFIG.LOADING_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{ Size = UDim2.new(1, 0, 1, 0) }
	)
	tween:Play()

	-- Remove loading screen when complete
	tween.Completed:Connect(function()
		task.wait(0.5) -- Brief pause
		removeLoadingScreen()
	end)
end

-- Function to remove loading screen
local function removeLoadingScreen()
	if not loadingGui then
		print("Loading screen already removed")
		return
	end

	print("Starting to remove loading screen...")
	isLoading = false -- Stop particle animations immediately

	-- Create fade out animation for the entire screen
	local background = loadingGui:FindFirstChild("Background")
	if background then
		local fadeOut = TweenService:Create(
			background,
			TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{ BackgroundTransparency = 1 }
		)

		-- Fade out all text and UI elements
		for _, child in pairs(loadingGui:GetDescendants()) do
			if child:IsA("GuiObject") and child ~= background then
				local childFade = TweenService:Create(
					child,
					TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{ BackgroundTransparency = 1, TextTransparency = 1 }
				)
				childFade:Play()
			end
		end

		fadeOut:Play()
		fadeOut.Completed:Connect(function()
			print("Fade out complete - destroying loading screen")
			loadingGui:Destroy()
			loadingGui = nil
			print("Loading screen successfully removed!")
		end)
	else
		-- Fallback: just destroy immediately if background not found
		print("Background not found - destroying immediately")
		loadingGui:Destroy()
		loadingGui = nil
	end
end

-- Function to start loading screen
function LoadingScreen.start()
	print("Starting loading screen...")

	-- Create loading screen
	createLoadingScreen()

	-- Start particle animation for each particle individually
	for _, particle in pairs(particles) do
		task.spawn(function()
			while isLoading and particle and particle.Parent do
				-- Random movement
				local newX = math.random()
				local newY = math.random()
				local animTime = math.random(3, 6)

				local moveTween = TweenService:Create(
					particle,
					TweenInfo.new(animTime, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
					{ Position = UDim2.new(newX, 0, newY, 0) }
				)
				moveTween:Play()

				-- Fade animation
				local fadeTween = TweenService:Create(
					particle,
					TweenInfo.new(animTime / 2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
					{ BackgroundTransparency = math.random(0.2, 0.7) }
				)
				fadeTween:Play()

				-- Wait for animation to complete
				moveTween.Completed:Wait()
				task.wait(math.random(0.5, 2)) -- Random delay before next animation
			end
		end)
	end

	-- Optional: Start automatic loading bar animation after delay
	-- Uncomment the lines below if you want automatic progression
	-- task.wait(5) -- Wait 5 seconds before auto-proceeding
	-- animateLoadingBar()
end

-- Auto-start loading screen
LoadingScreen.start()

print("Loading Screen system loaded")

return LoadingScreen
