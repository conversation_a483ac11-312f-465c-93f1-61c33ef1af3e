local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local camera = workspace.CurrentCamera

-- Configuration
local CONFIG = {
	HEIGHT = 20, -- Height above character (20 studs as requested)
	ANGLE = math.rad(90), -- 90 degrees for perfectly vertical top-down view
	SMOOTHNESS = 1, -- No smoothing - camera completely fixed on character
	OFFSET = Vector3.new(0, 0, 0), -- Additional offset from character
	MIN_HEIGHT = 15, -- Minimum camera height
	MAX_HEIGHT = 35, -- Maximum camera height
	ZOOM_SPEED = 2, -- Zoom sensitivity
}

-- Camera state
local currentCFrame = camera.CFrame
local targetCFrame = camera.CFrame
local isEnabled = false
local connections = {}

-- Top-down camera module
local TopDownCamera = {}

function TopDownCamera.updateCamera()
	local character = player.Character
	if not character or not isEnabled then
		return
	end

	local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
	if not humanoidRootPart then
		return
	end

	-- Calculate target position with offset (character centered)
	local characterPosition = humanoidRootPart.Position + CONFIG.OFFSET

	-- Position camera directly above character for true top-down view
	-- No forward/backward offset - purely vertical positioning
	local cameraPosition = characterPosition + Vector3.new(0, CONFIG.HEIGHT, 0)

	-- Create target CFrame looking straight down at character
	-- Using Vector3.new(0, 0, 1) as the up vector for correct orientation
	targetCFrame = CFrame.lookAt(cameraPosition, characterPosition, Vector3.new(0, 0, 1))

	-- Camera completely fixed on character - no smoothing/lerping
	currentCFrame = targetCFrame

	-- Apply to camera
	camera.CameraType = Enum.CameraType.Scriptable
	camera.CFrame = currentCFrame
end

function TopDownCamera.enable()
	if isEnabled then
		return
	end

	isEnabled = true
	camera.CameraType = Enum.CameraType.Scriptable

	-- Initialize camera position directly above character
	if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
		local hrp = player.Character.HumanoidRootPart
		-- Position camera directly above with no forward offset
		currentCFrame =
			CFrame.lookAt(hrp.Position + Vector3.new(0, CONFIG.HEIGHT, 0), hrp.Position, Vector3.new(0, 0, 1))
		camera.CFrame = currentCFrame
	end

	-- Connect update function
	connections.renderStep =
		RunService:BindToRenderStep("TopDownCamera", Enum.RenderPriority.Camera.Value, TopDownCamera.updateCamera)

	print("Top-down camera enabled")
end

function TopDownCamera.disable()
	if not isEnabled then
		return
	end

	isEnabled = false

	-- Disconnect update function
	if connections.renderStep then
		RunService:UnbindFromRenderStep("TopDownCamera")
		connections.renderStep = nil
	end

	-- Reset camera to default
	camera.CameraType = Enum.CameraType.Custom

	print("Top-down camera disabled")
end

function TopDownCamera.setHeight(height)
	CONFIG.HEIGHT = math.clamp(height, CONFIG.MIN_HEIGHT, CONFIG.MAX_HEIGHT)
end

function TopDownCamera.setAngle(angleDegrees)
	CONFIG.ANGLE = math.rad(math.clamp(angleDegrees, 30, 90))
end

function TopDownCamera.setSmoothness(smoothness)
	CONFIG.SMOOTHNESS = math.clamp(smoothness, 0.01, 1)
end

function TopDownCamera.setOffset(offset)
	CONFIG.OFFSET = offset or Vector3.new(0, 0, 0)
end

-- Handle character respawning
local function onCharacterAdded(character)
	if isEnabled then
		-- Wait for HumanoidRootPart to be added
		local humanoidRootPart = character:WaitForChild("HumanoidRootPart", 5)
		if humanoidRootPart then
			-- Reset camera position for new character - directly above
			currentCFrame = CFrame.lookAt(
				humanoidRootPart.Position + Vector3.new(0, CONFIG.HEIGHT, 0),
				humanoidRootPart.Position,
				Vector3.new(0, 0, 1)
			)
		end
	end
end

-- Connect character events
if player.Character then
	onCharacterAdded(player.Character)
end
player.CharacterAdded:Connect(onCharacterAdded)

-- Auto-enable the camera when module is loaded
TopDownCamera.enable()

-- Return module for external control (optional, for advanced usage)
return TopDownCamera
